const UserFriendlyErrorHandler = require('./user-friendly-error-handler');

// Initialize the error handler
const errorHandler = new UserFriendlyErrorHandler();

// Your main workflow function with user-friendly error handling
async function executeWorkflowWithFriendlyErrors(req, res) {
  const requestId = `REQ-${Date.now()}`;
  let currentStep = 'initialization';
  
  try {
    // Step 1: Create Session
    currentStep = 'authentication';
    console.log('🔐 Creating session...');
    const sessionId = await retryFunction(createSession);
    
    // Step 2: Get Auth Token
    currentStep = 'token_generation';
    console.log('🎫 Getting authentication token...');
    const token = await retryFunction(getAuthToken, 3, sessionId);
    
    // Step 3: Create Server/Object
    currentStep = 'object_creation';
    console.log('🏗️ Creating Adax object...');
    const adaxname = await retryFunction(createServer1, 3, token, req.body.InstanceName, req.body.ProvisioningEngineer);
    
    // Step 4: Execute SSM Document
    currentStep = 'ssm_execution';
    console.log('📋 Executing SSM document...');
    const executionResult = await executeSSMDocument(req.body.documentName, req.body.parameters);
    
    // Step 5: Store Results in S3
    currentStep = 'data_storage';
    console.log('💾 Storing results in S3...');
    const s3Location = await storeResultsInS3(executionResult, req.body.bucketName);
    
    // Step 6: Send Email Notification
    currentStep = 'notification';
    console.log('📧 Sending email notification...');
    await sendSuccessEmail(req.body.userEmail, {
      adaxname,
      executionResult,
      s3Location,
      requestId
    });
    
    // Success response
    res.status(200).json({
      success: true,
      message: 'Workflow completed successfully!',
      data: {
        adaxname,
        executionId: executionResult.executionId,
        s3Location,
        requestId
      }
    });
    
  } catch (error) {
    console.error(`❌ Error in step: ${currentStep}`, error);
    
    // Translate the error to user-friendly message
    const friendlyError = errorHandler.translateError(error, {
      operation: currentStep,
      requestId: requestId,
      documentName: req.body.documentName,
      instanceId: req.body.InstanceName,
      step: currentStep
    });
    
    // Log the friendly error for debugging
    console.log('User-friendly error:', errorHandler.formatForUser(friendlyError));
    
    // Send error email to user
    try {
      const emailContent = errorHandler.formatForEmail(friendlyError, {
        userName: req.body.userName || req.body.userEmail,
        requestId: requestId
      });
      
      await sendErrorEmail(req.body.userEmail, emailContent.subject, emailContent.body);
    } catch (emailError) {
      console.error('Failed to send error email:', emailError);
    }
    
    // Send user-friendly response
    res.status(400).json({
      success: false,
      error: {
        message: friendlyError.userMessage,
        suggestion: friendlyError.suggestion,
        category: friendlyError.category,
        requestId: requestId,
        step: currentStep
      }
    });
  }
}

// Enhanced retry function with step-specific error context
async function retryFunctionWithContext(fn, step, maxRetries = 3, ...args) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 ${step} - Attempt ${attempt}`);
      const result = await fn(...args);
      console.log(`✅ ${step} - Success on attempt ${attempt}`);
      return result;
    } catch (error) {
      lastError = error;
      console.log(`❌ ${step} - Failed on attempt ${attempt}: ${error.message}`);
      
      if (attempt === maxRetries) {
        // Add step context to the error
        error.step = step;
        error.attempts = maxRetries;
        throw error;
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

// Example of step-specific error handling
async function executeSSMDocument(documentName, parameters) {
  try {
    // Your SSM execution code here
    const result = await ssmClient.startAutomationExecution({
      DocumentName: documentName,
      Parameters: parameters
    }).promise();
    
    return result;
  } catch (error) {
    // Add context to help with error translation
    error.context = {
      documentName,
      parameterCount: Object.keys(parameters || {}).length
    };
    throw error;
  }
}

// Example of S3 storage with error context
async function storeResultsInS3(data, bucketName) {
  try {
    const key = `results/${Date.now()}-execution-results.csv`;
    
    const result = await s3Client.putObject({
      Bucket: bucketName,
      Key: key,
      Body: convertToCSV(data),
      ContentType: 'text/csv'
    }).promise();
    
    return `s3://${bucketName}/${key}`;
  } catch (error) {
    // Add context for better error messages
    error.context = {
      bucketName,
      operation: 'file_upload'
    };
    throw error;
  }
}

// Success email template
async function sendSuccessEmail(userEmail, data) {
  const subject = '✅ Your Automation Request Completed Successfully';
  const body = `
Dear User,

Great news! Your automation request has been completed successfully.

📋 Summary:
- Request ID: ${data.requestId}
- Adax Object: ${data.adaxname}
- Execution ID: ${data.executionResult.executionId}
- Results Location: ${data.s3Location}

You can download your results from the S3 location provided above.

If you have any questions, please contact our support team with your Request ID.

Best regards,
Automation Team
  `;
  
  return await sendEmail(userEmail, subject, body);
}

// Error email function
async function sendErrorEmail(userEmail, subject, body) {
  return await sendEmail(userEmail, subject, body);
}

// Usage example with your original code pattern:
if (req.body.isolated === 'false') {
  await executeWorkflowWithFriendlyErrors(req, res);
}

// Alternative: Just wrap your existing code with error translation
async function yourExistingWorkflow() {
  try {
    const sessionId = await createSession();
    const token = await getAuthToken(sessionId);
    console.log(token);
    adaxname = await createServer1(token, req.body.InstanceName, req.body.ProvisioningEngineer);
    console.log(adaxname);
    sendUpdate(`Adax Object is created ${adaxname}`);
  } catch (error) {
    // Translate and handle the error
    const friendlyError = errorHandler.translateError(error);
    
    // Send user-friendly message instead of technical error
    sendUpdate(`❌ ${friendlyError.userMessage}\n💡 ${friendlyError.suggestion}`);
    
    // Log technical details for debugging
    console.error('Technical error:', error);
    console.log('User-friendly error:', errorHandler.formatForUser(friendlyError));
    
    throw error; // Re-throw if needed
  }
}

module.exports = {
  executeWorkflowWithFriendlyErrors,
  retryFunctionWithContext,
  UserFriendlyErrorHandler
};
