const axios = require('axios');
const https = require('https');

// Configuration
const SERVER_URL = 'https://nox.secretservercloud.com/';
const API_KEY = 'AgJw-Dycmmj3FnMB1Ci7oJwYVhEkGYSxO_vUhCLbh4HL8DFhf-tlpKMCHEOVSPAAwHKV9RXbNpuWxZ4qFi4uOMDxDtzk5HCM-JR2xpp8Z4zGb3W1SBKXLarNBaDSD7tfo7XDoEpsDcj3EuRmVE2Z3VLw6zJbxuJAKYCuYGKwPt9bexGUPYW90ILgJNZtylUFAUnv28DDh9kI_b9A6CwD5853vJkBafBirc5h0MU7F4Qe5VKHwcQIpVZUaT7h7f29Gx53w3KO6qwOu0pnpz8RdtKKCTvT6lr0ZGyoOVqL3PX4Z6QhmwT6ssz35Vx7M2Id8GyIzh4q6ODBLuFm-AfcN8wceNQN9X3BtODuk-7h-jvvWI3iwbwjmBhloX6H-_0UCf3luAWXMt0i6TYIe-l_XXm63OQiWtbeaDNw0SAL3eiSKtXadZujcR7M-w6Kleou1IOcpDBovYEuUVPIdT0IbZSMsakmkx59IWTSszRbCaTAjJb39NmIPQTGrXvWuPQnSxBsAR9jfOlSfPSELAVPhbEDXBGTjwNDjGBHEXW3dIKz002mWY8_2Vf9o0U-zNlqwW_qLk5i1JHD-fPgROMDVAhpwkTChsbOd8kba5XPEAvjikyjwVeAZ20W6tN14B_WIcdeIWUUWwUg08x4kwjMBajKx-BvhJMEW-bqmJSzF4jTa9oyo6Z3c_89stn_2HXv9_eL3Axbm5ZGZT13tBB8_EgKJ-EWytmvcs_UftQywkjcCw';

// Create a custom HTTPS agent that ignores SSL certificate errors
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

async function createPasswordSecret(name, username, password, notes) {
  try {
    // 120 days from now
    const expirationDate = new Date(Date.now() + 120 * 24 * 60 * 60 * 1000).toISOString();

    const payload = {
      SecretTemplateId: 2,
      Name: name,
      SiteId: 1,
      FolderId: 6841,
      Items: [
        { FieldName: 'Username', ItemValue: username },
        { FieldName: 'Password', ItemValue: password },
        { FieldName: 'Notes', ItemValue: notes }
      ],
      ExpirationDate: expirationDate,
      EnableHeartbeat: true,
      HeartbeatEnabled: true
    };

    const response = await axios.post(`${SERVER_URL}/api/v1/secrets`, payload, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      httpsAgent: httpsAgent // Use the custom HTTPS agent to ignore SSL errors
    });

    console.log('Secret Created:', response.data);
    return response.data;

  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
    return null;
  }
}

// Call the function
createPasswordSecret(
   secret_name='U-manage Api key test',
        username='<EMAIL>',
        password='8LgZdBNI275JpPU/tw8s2w==:mSyuGM3sy3uJhKR9GaY3Y69NrDmG7pR6tL53RG8c3RcnAIfNXZ4ypztMLIjuCaAIa6hoVv6XJ86HcE0vSAUTyg==',
        notes='Auto Generated Key from U-manage'
);
