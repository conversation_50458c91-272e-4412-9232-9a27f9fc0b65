const axios = require('axios');
const https = require('https');

// Configuration
const SERVER_URL = 'https://nox.secretservercloud.com/';
const API_KEY = 'AgJw-Dycmmj3FnMB1Ci7oJwYVhEkGYSxO_vUhCLbh4HL8DFhf-tlpKMCHEOVSPAAwHKV9RXbNpuWxZ4qFi4uOMDxDtzk5HCM-JR2xpp8Z4zGb3W1SBKXLarNBaDSD7tfo7XDoEpsDcj3EuRmVE2Z3VLw6zJbxuJAKYCuYGKwPt9bexGUPYW90ILgJNZtylUFAUnv28DDh9kI_b9A6CwD5853vJkBafBirc5h0MU7F4Qe5VKHwcQIpVZUaT7h7f29Gx53w3KO6qwOu0pnpz8RdtKKCTvT6lr0ZGyoOVqL3PX4Z6QhmwT6ssz35Vx7M2Id8GyIzh4q6ODBLuFm-AfcN8wceNQN9X3BtODuk-7h-jvvWI3iwbwjmBhloX6H-_0UCf3luAWXMt0i6TYIe-l_XXm63OQiWtbeaDNw0SAL3eiSKtXadZujcR7M-w6Kleou1IOcpDBovYEuUVPIdT0IbZSMsakmkx59IWTSszRbCaTAjJb39NmIPQTGrXvWuPQnSxBsAR9jfOlSfPSELAVPhbEDXBGTjwNDjGBHEXW3dIKz002mWY8_2Vf9o0U-zNlqwW_qLk5i1JHD-fPgROMDVAhpwkTChsbOd8kba5XPEAvjikyjwVeAZ20W6tN14B_WIcdeIWUUWwUg08x4kwjMBajKx-BvhJMEW-bqmJSzF4jTa9oyo6Z3c_89stn_2HXv9_eL3Axbm5ZGZT13tBB8_EgKJ-EWytmvcs_UftQywkjcCw';

// Create a custom HTTPS agent that ignores SSL certificate errors
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

// Function to get the password based on secret ID
async function getSecretPassword(secretId) {
  try {
    const response = await axios.get(`${SERVER_URL}/api/v1/secrets/${secretId}`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      httpsAgent: httpsAgent
    });

    console.log('Secret Retrieved:', response.data);

    // Extract password from the items array
    const passwordItem = response.data.items.find(item => item.fieldName === 'Password');
    if (passwordItem) {
      console.log('Password:', passwordItem.itemValue);
      return passwordItem.itemValue;
    } else {
      console.log('Password field not found');
      return null;
    }

  } catch (error) {
    console.error('Error retrieving secret:', error.response?.data || error.message);
    return null;
  }
}

// Example usage: Replace 12345 with your actual secret ID
getSecretPassword(12345);