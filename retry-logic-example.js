// Retry function that attempts a function up to 3 times
async function retryFunction(fn, maxRetries = 3, ...args) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Attempt ${attempt} for ${fn.name}`);
      const result = await fn(...args);
      console.log(`✅ ${fn.name} succeeded on attempt ${attempt}`);
      return result;
    } catch (error) {
      lastError = error;
      console.log(`❌ ${fn.name} failed on attempt ${attempt}: ${error.message}`);
      
      if (attempt === maxRetries) {
        console.log(`🚫 ${fn.name} failed after ${maxRetries} attempts`);
        throw error;
      }
      
      // Optional: Add delay between retries (e.g., 1 second)
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

// Your original code with retry logic applied
if(req.body.isolated==='false'){
  try {
    const sessionId = await retryFunction(createSession);
    const token = await retryFunction(getAuthToken, 3, sessionId);
    console.log(token);
    adaxname = await retryFunction(createServer1, 3, token, req.body.InstanceName, req.body.ProvisioningEngineer);
    console.log(adaxname);
    sendUpdate(`Adax Object is created ${adaxname}`);
  } catch (error) {
    console.error('❌ All retry attempts failed:', error.message);
    sendUpdate(`Error: Failed to create Adax Object after 3 attempts - ${error.message}`);
    throw error; // Re-throw if you want the calling code to handle it
  }
}

// Alternative approach: More specific retry logic for each function
async function createSessionWithRetry() {
  return retryFunction(createSession);
}

async function getAuthTokenWithRetry(sessionId) {
  return retryFunction(getAuthToken, 3, sessionId);
}

async function createServer1WithRetry(token, instanceName, provisioningEngineer) {
  return retryFunction(createServer1, 3, token, instanceName, provisioningEngineer);
}

// Usage with the alternative approach:
if(req.body.isolated==='false'){
  try {
    const sessionId = await createSessionWithRetry();
    const token = await getAuthTokenWithRetry(sessionId);
    console.log(token);
    adaxname = await createServer1WithRetry(token, req.body.InstanceName, req.body.ProvisioningEngineer);
    console.log(adaxname);
    sendUpdate(`Adax Object is created ${adaxname}`);
  } catch (error) {
    console.error('❌ All retry attempts failed:', error.message);
    sendUpdate(`Error: Failed to create Adax Object after 3 attempts - ${error.message}`);
    throw error;
  }
}

// Advanced retry function with exponential backoff
async function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000, ...args) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Attempt ${attempt} for ${fn.name}`);
      const result = await fn(...args);
      console.log(`✅ ${fn.name} succeeded on attempt ${attempt}`);
      return result;
    } catch (error) {
      lastError = error;
      console.log(`❌ ${fn.name} failed on attempt ${attempt}: ${error.message}`);
      
      if (attempt === maxRetries) {
        console.log(`🚫 ${fn.name} failed after ${maxRetries} attempts`);
        throw error;
      }
      
      // Exponential backoff: delay increases with each retry
      const delay = baseDelay * Math.pow(2, attempt - 1);
      console.log(`⏳ Waiting ${delay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

// Usage with exponential backoff:
if(req.body.isolated==='false'){
  try {
    const sessionId = await retryWithBackoff(createSession, 3, 1000);
    const token = await retryWithBackoff(getAuthToken, 3, 1000, sessionId);
    console.log(token);
    adaxname = await retryWithBackoff(createServer1, 3, 1000, token, req.body.InstanceName, req.body.ProvisioningEngineer);
    console.log(adaxname);
    sendUpdate(`Adax Object is created ${adaxname}`);
  } catch (error) {
    console.error('❌ All retry attempts failed:', error.message);
    sendUpdate(`Error: Failed to create Adax Object after 3 attempts - ${error.message}`);
    throw error;
  }
}
