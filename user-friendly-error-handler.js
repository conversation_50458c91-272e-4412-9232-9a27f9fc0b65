// User-friendly error handler for SSM document execution workflow
class UserFriendlyErrorHandler {
  constructor() {
    this.errorMappings = {
      // Authentication & Authorization Errors
      'UnauthorizedOperation': {
        userMessage: 'Permission denied: Your account does not have the required permissions to perform this action.',
        suggestion: 'Please contact your administrator to grant you the necessary AWS permissions.',
        category: 'PERMISSION'
      },
      'AccessDenied': {
        userMessage: 'Access denied: You do not have permission to access the requested resource.',
        suggestion: 'Check if you have the correct IAM roles and policies assigned to your account.',
        category: 'PERMISSION'
      },
      'InvalidUserID.NotFound': {
        userMessage: 'User account not found: The specified user ID does not exist.',
        suggestion: 'Verify that you are using the correct user credentials and try again.',
        category: 'AUTHENTICATION'
      },

      // SSM Document Errors
      'InvalidDocument': {
        userMessage: 'Invalid automation document: The SSM document you specified is not valid or does not exist.',
        suggestion: 'Check the document name spelling and ensure the document exists in your AWS account.',
        category: 'DOCUMENT'
      },
      'DocumentAlreadyExists': {
        userMessage: 'Document already exists: A document with this name already exists.',
        suggestion: 'Use a different document name or update the existing document instead.',
        category: 'DOCUMENT'
      },
      'DocumentVersionLimitExceeded': {
        userMessage: 'Too many document versions: You have reached the maximum number of versions for this document.',
        suggestion: 'Delete some older versions of the document before creating a new one.',
        category: 'DOCUMENT'
      },

      // Execution Errors
      'InvalidExecutionId': {
        userMessage: 'Execution not found: The automation execution ID you provided does not exist or has expired.',
        suggestion: 'Check if the execution ID is correct or if the execution has been deleted due to retention policies.',
        category: 'EXECUTION'
      },
      'ExecutionLimitExceeded': {
        userMessage: 'Too many executions running: You have reached the maximum number of concurrent executions.',
        suggestion: 'Wait for some current executions to complete before starting new ones.',
        category: 'EXECUTION'
      },
      'AutomationExecutionLimitExceeded': {
        userMessage: 'Automation limit reached: You have exceeded the maximum number of automation executions.',
        suggestion: 'Wait for existing automations to complete or contact support to increase your limits.',
        category: 'EXECUTION'
      },

      // Instance/Target Errors
      'InvalidInstanceId': {
        userMessage: 'Server not found: The specified server/instance does not exist or is not accessible.',
        suggestion: 'Verify the server ID is correct and the server is running and accessible.',
        category: 'TARGET'
      },
      'UnsupportedPlatformType': {
        userMessage: 'Incompatible server type: The automation document is not compatible with this server type.',
        suggestion: 'Use a document designed for your server\'s operating system (Windows/Linux).',
        category: 'TARGET'
      },

      // Parameter Errors
      'InvalidParameters': {
        userMessage: 'Invalid input parameters: One or more of the parameters you provided are incorrect.',
        suggestion: 'Check all parameter values, formats, and ensure required parameters are provided.',
        category: 'PARAMETERS'
      },
      'ParameterNotFound': {
        userMessage: 'Missing required parameter: A required parameter is missing from your request.',
        suggestion: 'Review the document requirements and provide all mandatory parameters.',
        category: 'PARAMETERS'
      },

      // Storage/S3 Errors
      'NoSuchBucket': {
        userMessage: 'Storage location not found: The specified S3 bucket does not exist.',
        suggestion: 'Check the bucket name spelling or contact your administrator to create the bucket.',
        category: 'STORAGE'
      },
      'AccessDenied': {
        userMessage: 'Cannot save results: You do not have permission to write to the storage location.',
        suggestion: 'Contact your administrator to grant write permissions to the S3 bucket.',
        category: 'STORAGE'
      },
      'BucketNotEmpty': {
        userMessage: 'Storage location is not empty: Cannot perform operation on non-empty bucket.',
        suggestion: 'Clear the bucket contents or use a different storage location.',
        category: 'STORAGE'
      },

      // Email/Notification Errors
      'MessageRejected': {
        userMessage: 'Email delivery failed: The email could not be sent to the specified recipients.',
        suggestion: 'Check email addresses are valid and not blocked. Contact IT if the issue persists.',
        category: 'NOTIFICATION'
      },
      'SendingQuotaExceeded': {
        userMessage: 'Email limit reached: You have exceeded your daily email sending limit.',
        suggestion: 'Wait until tomorrow or contact support to increase your email quota.',
        category: 'NOTIFICATION'
      },

      // Network/Connectivity Errors
      'NetworkError': {
        userMessage: 'Network connection failed: Unable to connect to AWS services.',
        suggestion: 'Check your internet connection and try again. If the problem persists, contact IT support.',
        category: 'NETWORK'
      },
      'TimeoutError': {
        userMessage: 'Operation timed out: The request took too long to complete.',
        suggestion: 'The system may be busy. Please wait a few minutes and try again.',
        category: 'NETWORK'
      },

      // General AWS Errors
      'ThrottlingException': {
        userMessage: 'Service temporarily busy: AWS is currently limiting requests due to high traffic.',
        suggestion: 'Please wait a few minutes and try your request again.',
        category: 'RATE_LIMIT'
      },
      'ServiceUnavailable': {
        userMessage: 'Service temporarily unavailable: AWS service is currently experiencing issues.',
        suggestion: 'This is a temporary issue. Please try again in a few minutes.',
        category: 'SERVICE'
      }
    };
  }

  // Main method to convert technical errors to user-friendly messages
  translateError(error, context = {}) {
    const errorCode = this.extractErrorCode(error);
    const errorMessage = this.extractErrorMessage(error);
    
    // Try to find a specific mapping
    let mapping = this.errorMappings[errorCode];
    
    // If no specific mapping, try pattern matching
    if (!mapping) {
      mapping = this.findPatternMatch(errorMessage, errorCode);
    }
    
    // If still no mapping, create a generic one
    if (!mapping) {
      mapping = this.createGenericMapping(errorMessage, errorCode);
    }

    return {
      userMessage: mapping.userMessage,
      suggestion: mapping.suggestion,
      category: mapping.category,
      originalError: errorCode || errorMessage,
      context: context,
      timestamp: new Date().toISOString()
    };
  }

  // Extract error code from various error formats
  extractErrorCode(error) {
    if (typeof error === 'string') return error;
    if (error.code) return error.code;
    if (error.name) return error.name;
    if (error.errorCode) return error.errorCode;
    if (error.Code) return error.Code;
    return null;
  }

  // Extract error message from various error formats
  extractErrorMessage(error) {
    if (typeof error === 'string') return error;
    if (error.message) return error.message;
    if (error.Message) return error.Message;
    if (error.errorMessage) return error.errorMessage;
    return 'Unknown error occurred';
  }

  // Pattern matching for common error patterns
  findPatternMatch(errorMessage, errorCode) {
    const patterns = [
      {
        pattern: /permission|access.*denied|unauthorized/i,
        mapping: {
          userMessage: 'Permission denied: You do not have the required permissions for this action.',
          suggestion: 'Contact your administrator to grant you the necessary permissions.',
          category: 'PERMISSION'
        }
      },
      {
        pattern: /timeout|timed.*out/i,
        mapping: {
          userMessage: 'Operation timed out: The request took too long to complete.',
          suggestion: 'The system may be busy. Please wait and try again.',
          category: 'NETWORK'
        }
      },
      {
        pattern: /not.*found|does.*not.*exist/i,
        mapping: {
          userMessage: 'Resource not found: The requested item does not exist.',
          suggestion: 'Check that you have entered the correct name or ID and try again.',
          category: 'NOT_FOUND'
        }
      },
      {
        pattern: /invalid.*parameter|parameter.*invalid/i,
        mapping: {
          userMessage: 'Invalid input: One or more of your input values are incorrect.',
          suggestion: 'Check all your input values and ensure they meet the required format.',
          category: 'PARAMETERS'
        }
      },
      {
        pattern: /rate.*limit|throttl/i,
        mapping: {
          userMessage: 'Too many requests: You are making requests too quickly.',
          suggestion: 'Please wait a few minutes before trying again.',
          category: 'RATE_LIMIT'
        }
      }
    ];

    for (const { pattern, mapping } of patterns) {
      if (pattern.test(errorMessage) || pattern.test(errorCode)) {
        return mapping;
      }
    }

    return null;
  }

  // Create a generic mapping for unknown errors
  createGenericMapping(errorMessage, errorCode) {
    return {
      userMessage: 'An unexpected error occurred while processing your request.',
      suggestion: 'Please try again. If the problem continues, contact support with the error details below.',
      category: 'UNKNOWN'
    };
  }

  // Format the error for display to users
  formatForUser(translatedError) {
    return `
❌ ${translatedError.userMessage}

💡 What you can do:
${translatedError.suggestion}

🔍 Error Category: ${translatedError.category}
⏰ Time: ${new Date(translatedError.timestamp).toLocaleString()}

Technical Details (for support):
${translatedError.originalError}
    `.trim();
  }

  // Format error for email notifications
  formatForEmail(translatedError, userContext = {}) {
    return {
      subject: `Action Required: ${translatedError.category} Error in Your Request`,
      body: `
Dear ${userContext.userName || 'User'},

Your recent request could not be completed due to the following issue:

${translatedError.userMessage}

RECOMMENDED ACTION:
${translatedError.suggestion}

If you continue to experience issues after following the suggestion above, please contact our support team with the reference information below.

Reference Information:
- Error Category: ${translatedError.category}
- Time: ${new Date(translatedError.timestamp).toLocaleString()}
- Request ID: ${userContext.requestId || 'N/A'}

Technical Details:
${translatedError.originalError}

Best regards,
Automation Team
      `.trim()
    };
  }
}

// Export for use in other files
module.exports = UserFriendlyErrorHandler;

// Example usage:
/*
const errorHandler = new UserFriendlyErrorHandler();

try {
  // Your SSM/AWS operations here
} catch (error) {
  const friendlyError = errorHandler.translateError(error, {
    operation: 'SSM Document Execution',
    documentName: 'MyDocument',
    instanceId: 'i-1234567890'
  });
  
  console.log(errorHandler.formatForUser(friendlyError));
  
  // Send email notification
  const emailContent = errorHandler.formatForEmail(friendlyError, {
    userName: 'John Doe',
    requestId: 'REQ-12345'
  });
  
  sendEmail(emailContent.subject, emailContent.body);
}
*/
