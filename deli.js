const axios = require('axios');
 
// Configuration
const SERVER_URL = 'https://nox.secretservercloud.com/';
const API_KEY = 'AgJoesAm_9S4AmI1kYmiRFvgqGrcoQkzsYqG1UqS18Jsi6rRygv7JMJW3rc6iGD2CeOmcRfEOJR_a9CxTEVRfqUveekDQzeUOxQbCzps-Mp1YG6FL2V99ryfENKAjJod_bHLvTlLSZ7PD4WlBXqLXMv73EjK6UYzXSYwRCfsqz0K66WXFDqqBUbNTdJQrokJr64hkYHb4dQA75RPoTgCQk3jKmV6hXbX7dt3os_9Z7IqEV4rUj3R-oAVvZOYEan4WjBuhKqKZ_eR3zt-ir2Cpl8ka5X2YHdtg4zD1lk7116Yml9I3VmITgTJ48laabbOHccBMuovTIk8atg0yU_2JCuMQOQX1R6U_23fgEHYrJVo6fmaPdbjvZhzFFktFpr1fmusiGwUFezMy890IlzTB_mhQMxkd0cwj_xtVP1y6Nn2V61josqY6kIC7L7yXLzeI5YwOy0jfoyiLNB2VKNeSt5wcHbm0I4SF-jVpdXB2hA9DAHzo0kUpKVp-GiAjYaFeb_OBLewEOjBG17J4eAImYUN8RWfGZQb3hVvDrR4xoL2iBvXd9501WQNdB5Dr-iRhkxxeeiSmNptP4a1LNmXd75lvXOa_S8h3fQ9bf1CrJQhFDlhgWRGG00vSst8QnmKg0Y334YTfBvqDinGws1-4Ty6dYIb0MpEPSFV2dxem3XBbTFT5qVR2_dlCU7yuR__IcmW0fHGsxmHhG3MVSLdJFBC'
// You must set these values according to your company's Secret Server configuration
const SECRET_TEMPLATE_ID = 6062; // Example: 1 for "Generic Password", ask your admin for correct value
const SITE_ID = 1;

// Function to create a secret
async function createSecret(name, secretValue) {
  try {
    const payload = {
      SecretTemplateId: SECRET_TEMPLATE_ID,
      Name: name,
      SiteId: SITE_ID,
      Items: [
        { FieldName: 'Password', ItemValue: secretValue }
      ]
    };

    const response = await axios.post(`${SERVER_URL}/api/v1/secrets`, payload, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Secret Created:', response.data);
  } catch (error) {
    if (error.response) {
      console.error('Error status:', error.response.status);
      console.error('Error data:', error.response.data);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Request setup error:', error.message);
    }
  }
}

// Function to list all available secret templates
async function listSecretTemplates() {
  try {
    const response = await axios.get(`${SERVER_URL}/api/v1/secret-templates`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Available Templates:');
    response.data.records.forEach(template => {
      console.log(`ID: ${template.id}, Name: ${template.name}`);
    });
    return response.data;
  } catch (error) {
    console.error('Templates list error:', error.response?.data || error.message);
  }
}

// Function to list available folders
async function listFolders() {
  try {
    const response = await axios.get(`${SERVER_URL}/api/v1/folders`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Available Folders:');
    response.data.records.forEach(folder => {
      console.log(`ID: ${folder.id}, Name: ${folder.folderName}, Path: ${folder.folderPath}`);
    });

    // Use the user's personal folder (not the root Personal Folders)
    const userFolder = response.data.records.find(folder =>
      folder.id !== 1 && folder.folderPath.includes('Personal Folders')
    );

    if (userFolder) {
      console.log(`Using user folder: ${userFolder.id} (${userFolder.folderName})`);
      return userFolder.id;
    }

    // Otherwise use the last folder in the list
    const lastFolder = response.data.records[response.data.records.length - 1];
    console.log(`Using folder: ${lastFolder.id} (${lastFolder.folderName})`);
    return lastFolder?.id || 1;
  } catch (error) {
    console.error('Folders list error:', error.response?.data || error.message);
    return 1; // Default folder ID
  }
}

// Add this function to check template fields
async function getSecretTemplate(templateId) {
  try {
    const response = await axios.get(`${SERVER_URL}/api/v1/secret-templates/${templateId}`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`\nTemplate ID ${templateId} Details:`);
    console.log('Name:', response.data.name);
    console.log('Fields:');
    response.data.fields.forEach(field => {
      console.log(`  - ${field.displayName} (${field.name}) - Required: ${field.isRequired}`);
    });
    return response.data;
  } catch (error) {
    console.error('Template fetch error:', error.response?.data || error.message);
  }
}

// Function to try common template IDs
async function findWorkingTemplate() {
  const commonTemplateIds = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 6062];

  for (const templateId of commonTemplateIds) {
    console.log(`\nTrying template ID: ${templateId}`);
    const template = await getSecretTemplate(templateId);
    if (template) {
      console.log(`✓ Template ${templateId} is accessible!`);
      return templateId;
    }
  }
  return null;
}

// Function to create secret with proper fields based on template
async function createSecretWithTemplate(name, templateId, fieldValues = {}, folderId = 6841) {
  try {
    // Get template details first
    const template = await getSecretTemplate(templateId);
    if (!template) {
      console.error('Could not fetch template details');
      return null;
    }

    // Build Items array based on template fields
    const items = [];

    if (templateId === 1) { // Credit Card template
      items.push(
        { FieldName: 'Number', ItemValue: fieldValues.cardNumber || '****************' },
        { FieldName: 'ExpirationDate', ItemValue: fieldValues.expirationDate || '12/25' },
        { FieldName: 'FullName', ItemValue: fieldValues.fullName || 'Test User' },
        { FieldName: 'CardType', ItemValue: fieldValues.cardType || 'Visa' },
        { FieldName: 'Notes', ItemValue: fieldValues.notes || 'Test credit card secret' }
      );
    } else {
      // For other templates, try to use common field names
      template.fields.forEach(field => {
        if (field.isRequired) {
          let value = fieldValues[field.name] || 'test-value';
          if (field.name.toLowerCase().includes('password')) {
            value = fieldValues.password || 'defaultPassword123';
          }
          items.push({ FieldName: field.name, ItemValue: value });
        }
      });
    }

    const payload = {
      SecretTemplateId: templateId,
      Name: name,
      SiteId: SITE_ID,
      FolderId: folderId,
      Items: items
    };

    console.log('Creating secret with payload:', JSON.stringify(payload, null, 2));

    const response = await axios.post(`${SERVER_URL}/api/v1/secrets`, payload, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✓ Secret Created Successfully!');
    console.log('Secret ID:', response.data.id);
    console.log('Secret Name:', response.data.name);
    return response.data;
  } catch (error) {
    if (error.response) {
      console.error('Error status:', error.response.status);
      console.error('Error data:', error.response.data);
    } else {
      console.error('Request error:', error.message);
    }
    return null;
  }
}

// Main execution function
async function main() {
  console.log('=== Listing Available Templates ===');
  await listSecretTemplates();

  console.log('\n=== Listing Available Folders ===');
  const folderId = await listFolders();

  console.log('\n=== Finding Working Template ===');
  const workingTemplateId = await findWorkingTemplate();

  if (workingTemplateId) {
    console.log(`\n=== Creating Secret with Template ${workingTemplateId} in Folder ${folderId} ===`);

    if (workingTemplateId === 1) {
      // Credit Card template
      await createSecretWithTemplate('Test Credit Card', workingTemplateId, {
        cardNumber: '****************',
        expirationDate: '12/25',
        fullName: 'John Doe',
        cardType: 'Visa',
        notes: 'Test credit card created via API'
      }, folderId);
    } else {
      // Generic template
      await createSecretWithTemplate('Test Secret', workingTemplateId, {
        password: 'qwertyuiopas'
      }, folderId);
    }
  } else {
    console.log('\n❌ No accessible templates found. Please check your permissions or contact your admin.');
  }
}

// Run the main function
main();
// Example
